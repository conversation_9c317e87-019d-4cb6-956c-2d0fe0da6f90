/**
 * Enhanced Document Parser Service
 * 增强文档解析服务 - 支持 .mindmap 格式文件解析和渲染
 * 
 * 功能特性：
 * - 识别和解析 .mindmap 后缀文件
 * - 转换为 SimpleMindMap 兼容格式
 * - 后台数据处理，不修改源文件
 * - 支持多种思维导图数据格式
 */

import { MindMapNode, ParsedDocument, DocumentParseResult, FileTypeInfo } from '../types';

/**
 * 支持的文档格式枚举
 */
export enum SupportedFormat {
  MINDMAP = 'mindmap',
  XMIND = 'xmind',
  JSON = 'json',
  MARKDOWN = 'markdown'
}

/**
 * 解析配置选项
 */
export interface ParseOptions {
  preserveOriginal?: boolean;    // 是否保留原始数据
  validateStructure?: boolean;   // 是否验证数据结构
  enableCache?: boolean;         // 是否启用缓存
  maxFileSize?: number;          // 最大文件大小限制 (bytes)
}

/**
 * 文件类型检测结果
 */
export interface FileTypeDetection {
  format: SupportedFormat;
  confidence: number;            // 置信度 0-1
  mimeType?: string;
  encoding?: string;
}

/**
 * 增强文档解析器类
 */
export class EnhancedDocumentParser {
  private static instance: EnhancedDocumentParser;
  private parseCache = new Map<string, DocumentParseResult>();
  
  private constructor() {}
  
  /**
   * 获取单例实例
   */
  public static getInstance(): EnhancedDocumentParser {
    if (!EnhancedDocumentParser.instance) {
      EnhancedDocumentParser.instance = new EnhancedDocumentParser();
    }
    return EnhancedDocumentParser.instance;
  }

  /**
   * 检测文件类型
   * @param file 文件对象或文件名
   * @returns 文件类型检测结果
   */
  public detectFileType(file: File | string): FileTypeDetection {
    const fileName = typeof file === 'string' ? file : file.name;
    const extension = this.getFileExtension(fileName).toLowerCase();
    
    switch (extension) {
      case 'mindmap':
        return {
          format: SupportedFormat.MINDMAP,
          confidence: 1.0,
          mimeType: 'application/json'
        };
      case 'xmind':
        return {
          format: SupportedFormat.XMIND,
          confidence: 1.0,
          mimeType: 'application/zip'
        };
      case 'json':
        return {
          format: SupportedFormat.JSON,
          confidence: 0.8,
          mimeType: 'application/json'
        };
      case 'md':
      case 'markdown':
        return {
          format: SupportedFormat.MARKDOWN,
          confidence: 0.7,
          mimeType: 'text/markdown'
        };
      default:
        throw new Error(`Unsupported file format: ${extension}`);
    }
  }

  /**
   * 解析文档文件
   * @param file 文件对象
   * @param options 解析选项
   * @returns 解析结果
   */
  public async parseDocument(
    file: File, 
    options: ParseOptions = {}
  ): Promise<DocumentParseResult> {
    const defaultOptions: ParseOptions = {
      preserveOriginal: true,
      validateStructure: true,
      enableCache: true,
      maxFileSize: 50 * 1024 * 1024, // 50MB
      ...options
    };

    // 文件大小检查
    if (file.size > defaultOptions.maxFileSize!) {
      throw new Error(`File size exceeds limit: ${file.size} bytes`);
    }

    // 缓存检查
    const cacheKey = this.generateCacheKey(file);
    if (defaultOptions.enableCache && this.parseCache.has(cacheKey)) {
      return this.parseCache.get(cacheKey)!;
    }

    try {
      // 检测文件类型
      const fileType = this.detectFileType(file);
      
      // 读取文件内容
      const content = await this.readFileContent(file, fileType);
      
      // 解析内容
      const parsedData = await this.parseContent(content, fileType);
      
      // 验证数据结构
      if (defaultOptions.validateStructure) {
        this.validateMindMapStructure(parsedData);
      }

      // 构建解析结果
      const result: DocumentParseResult = {
        success: true,
        data: parsedData,
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          format: fileType.format,
          parseTime: Date.now(),
          originalContent: defaultOptions.preserveOriginal ? content : undefined
        }
      };

      // 缓存结果
      if (defaultOptions.enableCache) {
        this.parseCache.set(cacheKey, result);
      }

      return result;

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown parsing error',
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          parseTime: Date.now()
        }
      };
    }
  }

  /**
   * 解析 .mindmap 格式文件
   * @param content 文件内容
   * @returns SimpleMindMap 兼容的数据结构
   */
  private async parseMindMapFormat(content: string): Promise<MindMapNode> {
    try {
      // 首先尝试解析 JSON 格式
      try {
        const jsonData = JSON.parse(content);
        
        // 检查是否为 SimpleMindMap 格式
        if (this.isSimpleMindMapFormat(jsonData)) {
          return jsonData;
        }
        
        // 检查是否为其他思维导图格式，进行转换
        if (this.isXMindFormat(jsonData)) {
          return this.convertXMindToSimpleMindMap(jsonData);
        }
        
        // 检查是否为通用思维导图格式
        if (this.isGenericMindMapFormat(jsonData)) {
          return this.convertGenericToSimpleMindMap(jsonData);
        }
        
        throw new Error('Unrecognized JSON mindmap format');
        
      } catch (jsonError) {
        // JSON 解析失败，使用自制解析器
        console.log('JSON parsing failed, using custom parser...');
        console.log('🔍 原始内容长度:', content.length, '预览:', content.substring(0, 200));

        // 确保内容有一级标题，如果没有则添加默认标题
        let processedContent = content.trim();
        if (!processedContent.match(/^#\s+/m)) {
          processedContent = '# 思维导图\n\n' + processedContent;
          console.log('🔧 添加了默认一级标题');
        }

        const result = this.parseMarkdownToMindMap(processedContent);

        console.log('🔍 自制解析结果:', {
          hasChildren: !!(result.children && result.children.length > 0),
          childrenCount: result.children?.length || 0,
          rootText: result.data?.text || 'no text'
        });

        return result;
      }
      
    } catch (error) {
      throw new Error(`Failed to parse mindmap content: ${error}`);
    }
  }

  /**
   * 处理Markdown格式化文本
   * @param text 原始文本
   * @returns 处理后的文本和样式信息
   */
  private processMarkdownFormatting(text: string): { text: string; style?: any } {
    let processedText = text;
    const style: any = {};

    // 处理粗体 **text** 或 __text__
    const boldMatch = processedText.match(/\*\*(.*?)\*\*|__(.*?)__/g);
    if (boldMatch) {
      style.fontWeight = 'bold';
      processedText = processedText.replace(/\*\*(.*?)\*\*|__(.*?)__/g, '$1$2');
    }

    // 处理斜体 *text* 或 _text_ (但不是粗体的一部分)
    const italicMatch = processedText.match(/(?<!\*)\*([^*]+?)\*(?!\*)|(?<!_)_([^_]+?)_(?!_)/g);
    if (italicMatch) {
      style.fontStyle = 'italic';
      processedText = processedText.replace(/(?<!\*)\*([^*]+?)\*(?!\*)|(?<!_)_([^_]+?)_(?!_)/g, '$1$2');
    }

    // 处理删除线 ~~text~~
    const strikeMatch = processedText.match(/~~(.*?)~~/g);
    if (strikeMatch) {
      style.textDecoration = 'line-through';
      processedText = processedText.replace(/~~(.*?)~~/g, '$1');
    }

    // 处理行内代码 `code`
    const codeMatch = processedText.match(/`([^`]+?)`/g);
    if (codeMatch) {
      style.fontFamily = 'monospace';
      style.backgroundColor = '#f5f5f5';
      style.padding = '2px 4px';
      style.borderRadius = '3px';
      processedText = processedText.replace(/`([^`]+?)`/g, '$1');
    }

    // 处理链接 [text](url)
    const linkMatch = processedText.match(/\[([^\]]+?)\]\([^)]+?\)/g);
    if (linkMatch) {
      style.color = '#0066cc';
      style.textDecoration = 'underline';
      processedText = processedText.replace(/\[([^\]]+?)\]\([^)]+?\)/g, '$1');
    }

    // 处理高亮 ==text==
    const highlightMatch = processedText.match(/==(.*?)==/g);
    if (highlightMatch) {
      style.backgroundColor = '#ffff00';
      processedText = processedText.replace(/==(.*?)==/g, '$1');
    }

    return {
      text: processedText.trim(),
      style: Object.keys(style).length > 0 ? style : undefined
    };
  }

  /**
   * 将 Markdown 格式转换为思维导图数据结构（公共方法）
   * @param content Markdown 内容
   * @returns SimpleMindMap 兼容的数据结构
   */
  public parseMarkdownToMindMap(content: string): MindMapNode {
    const lines = content.split('\n');
    
    if (lines.length === 0) {
      return {
        data: { 
          text: '空白思维导图',
          expand: true // 确保节点默认展开状态，支持展开按钮
        },
        children: []
      };
    }

    // 解析标题层级和列表层级
    const nodeStack: { node: MindMapNode; level: number; type: 'header' | 'list' }[] = [];
    const listStack: { node: MindMapNode; indentLevel: number }[] = []; // 专门用于列表项的层级管理
    let rootNode: MindMapNode | null = null;
    let currentTextBuffer: string[] = []; // 用于收集普通文本

    const flushTextBuffer = () => {
      if (currentTextBuffer.length > 0 && nodeStack.length > 0) {
        const parent = nodeStack[nodeStack.length - 1].node;
        const combinedText = currentTextBuffer.join(' ').trim();
        if (combinedText) {
          const processed = this.processMarkdownFormatting(combinedText);
          const textNode: MindMapNode = {
            data: { 
              text: processed.text,
              expand: true, // 确保节点默认展开状态，支持展开按钮
              ...(processed.style && { style: processed.style })
            },
            children: []
          };
          if (!parent.children) parent.children = [];
          parent.children.push(textNode);
        }
        currentTextBuffer = [];
      }
    };

    // 计算行的缩进级别（每2个空格或1个tab为一级）
    const getIndentLevel = (line: string): number => {
      const match = line.match(/^(\s*)/);
      if (!match) return 0;
      const spaces = match[1];
      // 计算缩进级别：2个空格或1个tab = 1级
      return Math.floor(spaces.replace(/\t/g, '  ').length / 2);
    };

    for (const line of lines) {
      const trimmed = line.trim();
      const indentLevel = getIndentLevel(line);
      
      // 检测标题级别
      const headerMatch = trimmed.match(/^(#{1,6})\s+(.+)$/);
      if (headerMatch) {
        // 先处理缓存的文本
        flushTextBuffer();
        // 清空列表栈，因为标题会重置列表层级
        listStack.length = 0;
        
        const level = headerMatch[1].length;
        const rawText = headerMatch[2].trim();
        const processed = this.processMarkdownFormatting(rawText);

        const newNode: MindMapNode = {
          data: { 
            text: processed.text,
            expand: true, // 确保节点默认展开状态，支持展开按钮
            ...(processed.style && { style: processed.style })
          },
          children: []
        };

        if (!rootNode) {
          // 如果还没有根节点，第一个标题成为根节点（无论级别）
          rootNode = newNode;
          nodeStack.length = 0; // 清空栈
          nodeStack.push({ node: newNode, level, type: 'header' });
        } else if (level === 1) {
          // 新的一级标题，替换根节点
          rootNode = newNode;
          nodeStack.length = 0; // 清空栈
          nodeStack.push({ node: newNode, level, type: 'header' });
        } else {
          // 子节点
          // 找到合适的父节点（只考虑标题节点）
          while (nodeStack.length > 0 && 
                 (nodeStack[nodeStack.length - 1].level >= level || 
                  nodeStack[nodeStack.length - 1].type === 'list')) {
            nodeStack.pop();
          }

          if (nodeStack.length > 0) {
            const parent = nodeStack[nodeStack.length - 1].node;
            if (!parent.children) parent.children = [];
            parent.children.push(newNode);
          } else if (rootNode) {
            // 如果没有合适的父节点，添加到根节点
            if (!rootNode.children) rootNode.children = [];
            rootNode.children.push(newNode);
          }

          nodeStack.push({ node: newNode, level, type: 'header' });
        }
      } else {
        // 处理有序列表（支持缩进）
        const orderedListMatch = line.match(/^(\s*)(\d+)\.\s+(.+)$/);
        if (orderedListMatch) {
          flushTextBuffer();
          
          const rawText = orderedListMatch[3].trim();
          const processed = this.processMarkdownFormatting(rawText);
          const newNode: MindMapNode = {
            data: { 
              text: `${orderedListMatch[2]}. ${processed.text}`,
              expand: true, // 确保节点默认展开状态，支持展开按钮
              ...(processed.style && { style: processed.style })
            },
            children: []
          };

          // 处理列表项的层级关系
          this.handleListItemHierarchy(newNode, indentLevel, listStack, nodeStack, rootNode);
        } else {
          // 处理无序列表项（支持缩进和任务状态标记）
          const listMatch = line.match(/^(\s*)[-*+]\s+(.+)$/);
          if (listMatch) {
            // 先处理缓存的文本
            flushTextBuffer();

            let rawText = listMatch[2].trim();
            let isTask = false;
            let isChecked = false;

            // 检查是否为任务状态标记
            const taskMatch = rawText.match(/^(\[[ xX]\])\s+(.+)$/);
            if (taskMatch) {
              isTask = true;
              isChecked = taskMatch[1] !== '[ ]';
              rawText = taskMatch[2].trim();
            }

            const processed = this.processMarkdownFormatting(rawText);
            const newNode: MindMapNode = {
              data: {
                text: processed.text,
                expand: true, // 确保节点默认展开状态，支持展开按钮
                ...(processed.style && { style: processed.style }),
                // 添加任务状态标记数据
                ...(isTask && {
                  checkbox: isChecked,
                  task: true
                })
              },
              children: []
            };

            // 处理列表项的层级关系
            this.handleListItemHierarchy(newNode, indentLevel, listStack, nodeStack, rootNode);
          } else {
            // 处理直接的任务状态标记（不带 - 前缀）
            const directTaskMatch = line.match(/^(\s*)(\[[ xX]\])\s+(.+)$/);
            if (directTaskMatch) {
              // 先处理缓存的文本
              flushTextBuffer();

              const indentLevel = Math.floor(directTaskMatch[1].length / 2);
              const isChecked = directTaskMatch[2] !== '[ ]';
              const rawText = directTaskMatch[3].trim();

              const processed = this.processMarkdownFormatting(rawText);
              const newNode: MindMapNode = {
                data: {
                  text: processed.text,
                  expand: true, // 确保节点默认展开状态，支持展开按钮
                  ...(processed.style && { style: processed.style }),
                  // 添加任务状态标记数据
                  checkbox: isChecked,
                  task: true
                },
                children: []
              };

              // 处理列表项的层级关系
              this.handleListItemHierarchy(newNode, indentLevel, listStack, nodeStack, rootNode);
            } else if (trimmed) {
              // 处理引用块 > text
              const quoteMatch = trimmed.match(/^>\s*(.+)$/);
              if (quoteMatch) {
                flushTextBuffer();

                const rawText = quoteMatch[1].trim();
                const processed = this.processMarkdownFormatting(rawText);
                const newNode: MindMapNode = {
                  data: {
                    text: processed.text,
                    expand: true, // 确保节点默认展开状态，支持展开按钮
                    ...(processed.style && {
                      style: {
                        ...processed.style,
                        fontStyle: 'italic',
                        color: '#666666',
                        borderLeft: '3px solid #ddd',
                        paddingLeft: '10px'
                      }
                    })
                  },
                  children: []
                };

                if (nodeStack.length > 0) {
                  const parent = nodeStack[nodeStack.length - 1].node;
                  if (!parent.children) parent.children = [];
                  parent.children.push(newNode);
                } else if (rootNode) {
                  if (!rootNode.children) rootNode.children = [];
                  rootNode.children.push(newNode);
                }
              }
            } else {
              // 普通文本行，添加到文本缓冲区
              currentTextBuffer.push(trimmed);
            }
          } else {
            // 空行，处理缓存的文本
            flushTextBuffer();
          }
        }
      }
    }

    // 处理剩余的文本缓冲区
    flushTextBuffer();

    // 如果没有根节点，创建一个默认的
    if (!rootNode) {
      const firstLine = lines.find(line => line.trim())?.trim() || '思维导图';
      const processed = this.processMarkdownFormatting(firstLine);
      rootNode = {
        data: { 
          text: processed.text,
          expand: true, // 确保节点默认展开状态，支持展开按钮
          ...(processed.style && { style: processed.style })
        },
        children: []
      };
    }

    return rootNode;
  }

  /**
   * 处理列表项的层级关系
   * @param newNode 新的列表项节点
   * @param indentLevel 缩进级别
   * @param listStack 列表项层级栈
   * @param nodeStack 总体节点栈
   * @param rootNode 根节点
   */
  private handleListItemHierarchy(
    newNode: MindMapNode,
    indentLevel: number,
    listStack: { node: MindMapNode; indentLevel: number }[],
    nodeStack: { node: MindMapNode; level: number; type: 'header' | 'list' }[],
    rootNode: MindMapNode | null
  ): void {
    // 清理列表栈，移除缩进级别大于等于当前级别的项
    while (listStack.length > 0 && listStack[listStack.length - 1].indentLevel >= indentLevel) {
      listStack.pop();
    }

    let parentNode: MindMapNode | null = null;

    if (listStack.length > 0) {
      // 如果列表栈中有父级列表项，添加为其子节点
      parentNode = listStack[listStack.length - 1].node;
    } else {
      // 如果没有父级列表项，寻找最近的标题节点或根节点
      for (let i = nodeStack.length - 1; i >= 0; i--) {
        if (nodeStack[i].type === 'header') {
          parentNode = nodeStack[i].node;
          break;
        }
      }
      
      // 如果没有找到标题节点，使用根节点
      if (!parentNode && rootNode) {
        parentNode = rootNode;
      }
    }

    // 添加到父节点
    if (parentNode) {
      if (!parentNode.children) parentNode.children = [];
      parentNode.children.push(newNode);
    }

    // 将当前节点添加到列表栈和总栈
    listStack.push({ node: newNode, indentLevel });
    
    // 更新总栈，移除列表类型的项并添加当前项
    while (nodeStack.length > 0 && nodeStack[nodeStack.length - 1].type === 'list') {
      nodeStack.pop();
    }
    nodeStack.push({ node: newNode, level: indentLevel + 10, type: 'list' }); // +10 确保列表项不会与标题冲突
  }

  /**
   * 读取文件内容
   * @param file 文件对象
   * @param fileType 文件类型信息
   * @returns 文件内容字符串
   */
  private async readFileContent(file: File, fileType: FileTypeDetection): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const content = event.target?.result as string;
        resolve(content);
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read file content'));
      };
      
      // 根据文件类型选择读取方式
      if (fileType.format === SupportedFormat.XMIND) {
        reader.readAsArrayBuffer(file);
      } else {
        reader.readAsText(file, 'utf-8');
      }
    });
  }

  /**
   * 解析内容
   * @param content 文件内容
   * @param fileType 文件类型
   * @returns 解析后的思维导图数据
   */
  private async parseContent(content: string, fileType: FileTypeDetection): Promise<MindMapNode> {
    switch (fileType.format) {
      case SupportedFormat.MINDMAP:
        return this.parseMindMapFormat(content);
      
      case SupportedFormat.JSON:
        return this.parseJSONFormat(content);
      
      case SupportedFormat.MARKDOWN:
        return this.parseMarkdownFormat(content);
      
      case SupportedFormat.XMIND:
        return this.parseXMindFormat(content);
      
      default:
        throw new Error(`Unsupported format: ${fileType.format}`);
    }
  }

  /**
   * 解析 JSON 格式
   */
  private async parseJSONFormat(content: string): Promise<MindMapNode> {
    const jsonData = JSON.parse(content);
    
    if (this.isSimpleMindMapFormat(jsonData)) {
      return jsonData;
    }
    
    // 尝试转换为 SimpleMindMap 格式
    return this.convertGenericToSimpleMindMap(jsonData);
  }

  /**
   * 解析 Markdown 格式（简单实现）
   */
  private async parseMarkdownFormat(content: string): Promise<MindMapNode> {
    const lines = content.split('\n').filter(line => line.trim());
    const root: MindMapNode = {
      data: { 
        text: 'Root',
        expand: true // 确保节点默认展开状态，支持展开按钮
      },
      children: []
    };

    let currentLevel = 0;
    const nodeStack: MindMapNode[] = [root];

    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed) continue;

      // 检测标题级别
      const headerMatch = trimmed.match(/^(#{1,6})\s+(.+)$/);
      if (headerMatch) {
        const level = headerMatch[1].length;
        const text = headerMatch[2];

        const node: MindMapNode = {
          data: { 
            text,
            expand: true // 确保节点默认展开状态，支持展开按钮
          },
          children: []
        };

        // 调整节点层级
        while (nodeStack.length > level) {
          nodeStack.pop();
        }

        const parent = nodeStack[nodeStack.length - 1];
        if (!parent.children) parent.children = [];
        parent.children.push(node);
        nodeStack.push(node);
      }
    }

    return root;
  }

  /**
   * 解析 XMind 格式（需要额外处理）
   */
  private async parseXMindFormat(content: string): Promise<MindMapNode> {
    // XMind 文件是 ZIP 格式，需要特殊处理
    // 这里提供基础框架，实际实现需要 ZIP 解析库
    throw new Error('XMind format parsing requires additional ZIP processing library');
  }

  /**
   * 检查是否为 SimpleMindMap 格式
   */
  private isSimpleMindMapFormat(data: any): boolean {
    return data && 
           typeof data === 'object' && 
           data.data && 
           typeof data.data.text === 'string';
  }

  /**
   * 检查是否为 XMind 格式
   */
  private isXMindFormat(data: any): boolean {
    return data && data.rootTopic && data.rootTopic.title;
  }

  /**
   * 检查是否为通用思维导图格式
   */
  private isGenericMindMapFormat(data: any): boolean {
    return data && (data.title || data.name || data.text);
  }

  /**
   * 转换 XMind 格式到 SimpleMindMap
   */
  private convertXMindToSimpleMindMap(xmindData: any): MindMapNode {
    const convertNode = (node: any): MindMapNode => {
      const result: MindMapNode = {
        data: {
          text: node.title || node.text || 'Untitled',
          expand: true // 确保节点默认展开状态，支持展开按钮
        }
      };

      if (node.children && Array.isArray(node.children)) {
        result.children = node.children.map(convertNode);
      }

      return result;
    };

    return convertNode(xmindData.rootTopic || xmindData);
  }

  /**
   * 转换通用格式到 SimpleMindMap
   */
  private convertGenericToSimpleMindMap(genericData: any): MindMapNode {
    const convertNode = (node: any): MindMapNode => {
      const result: MindMapNode = {
        data: {
          text: node.title || node.name || node.text || 'Untitled',
          expand: true // 确保节点默认展开状态，支持展开按钮
        }
      };

      // 处理子节点
      const childrenKey = node.children || node.subtopics || node.nodes;
      if (childrenKey && Array.isArray(childrenKey)) {
        result.children = childrenKey.map(convertNode);
      }

      return result;
    };

    return convertNode(genericData);
  }

  /**
   * 验证思维导图数据结构
   */
  private validateMindMapStructure(data: MindMapNode): void {
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid mindmap data: must be an object');
    }

    if (!data.data || typeof data.data.text !== 'string') {
      throw new Error('Invalid mindmap data: missing or invalid text property');
    }

    // 递归验证子节点
    if (data.children) {
      if (!Array.isArray(data.children)) {
        throw new Error('Invalid mindmap data: children must be an array');
      }
      
      data.children.forEach((child, index) => {
        try {
          this.validateMindMapStructure(child);
        } catch (error) {
          throw new Error(`Invalid child node at index ${index}: ${error}`);
        }
      });
    }
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot === -1 ? '' : fileName.substring(lastDot + 1);
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(file: File): string {
    return `${file.name}_${file.size}_${file.lastModified}`;
  }

  /**
   * 清除解析缓存
   */
  public clearCache(): void {
    this.parseCache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.parseCache.size,
      keys: Array.from(this.parseCache.keys())
    };
  }
}

/**
 * 导出单例实例
 */
export const enhancedDocumentParser = EnhancedDocumentParser.getInstance();

/**
 * 便捷的解析函数
 * @param file 要解析的文件
 * @param options 解析选项
 * @returns 解析结果
 */
export async function parseDocumentFile(
  file: File, 
  options?: ParseOptions
): Promise<DocumentParseResult> {
  return enhancedDocumentParser.parseDocument(file, options);
}

/**
 * 检测文件是否为支持的思维导图格式
 * @param fileName 文件名
 * @returns 是否支持
 */
export function isSupportedMindMapFile(fileName: string): boolean {
  try {
    const detection = enhancedDocumentParser.detectFileType(fileName);
    return detection.confidence > 0.5;
  } catch {
    return false;
  }
}