/**
 * TaskPlannerAgent - 智能任务规划服务
 * 负责将用户目标转换为结构化的思维导图规划
 * 集成现有的brainstormingHelperService和人设系统
 */

import {
  DatabaseResult,
  HelperRequest,
  PersonaConfig,
  MindMapNode,
  AIMessage
} from '@/types';
import {
  AgentResult,
  PersonaSelectionMode,
  PersonaMatchingRule
} from '@/types/agent.types';
import { AIService } from '@/services/aiService';
import { PersonaService } from '@/services/personaService';

/**
 * 任务规划配置接口
 */
export interface TaskPlanningConfig {
  /** 人设选择模式 */
  personaMode: PersonaSelectionMode;
  /** 指定的人设ID（手动模式时使用） */
  specificPersonaId?: string;
  /** 是否启用多轮优化 */
  enableMultiRound: boolean;
  /** 最大规划深度 */
  maxPlanningDepth: number;
  /** 是否包含时间估算 */
  includeTimeEstimation: boolean;
}

/**
 * 规划结果接口（简化版）
 */
export interface PlanningResult {
  /** 生成的Markdown内容 */
  markdownContent: string;
  /** 使用的人设信息 */
  usedPersona: PersonaConfig;
  /** 规划统计信息 */
  statistics: {
    contentLength: number;
    planningTime: number;
    aiCallCount: number;
    tokensUsed: number;
  };
  /** 规划元数据 */
  metadata: {
    planningApproach: string;
    confidenceScore: number;
    suggestedNextSteps?: string[];
    fileName: string;
  };
}

export class TaskPlannerAgent {
  private aiService: AIService;
  private personaService: PersonaService;
  private defaultConfig: TaskPlanningConfig;

  // 内置的人设匹配规则
  private personaMatchingRules: PersonaMatchingRule[] = [
    {
      id: 'education-planning',
      name: '教育规划匹配',
      keywords: ['学习', '教育', '课程', '培训', '技能', '知识'],
      targetPersonaId: 'education-expert',
      weight: 0.8,
      enabled: true
    },
    {
      id: 'project-management',
      name: '项目管理匹配',
      keywords: ['项目', '管理', '计划', '执行', '团队', '进度'],
      targetPersonaId: 'project-manager',
      weight: 0.9,
      enabled: true
    },
    {
      id: 'creative-writing',
      name: '创意写作匹配',
      keywords: ['写作', '创作', '文章', '故事', '内容', '创意'],
      targetPersonaId: 'creative-writer',
      weight: 0.7,
      enabled: true
    },
    {
      id: 'technical-development',
      name: '技术开发匹配',
      keywords: ['开发', '编程', '技术', '代码', '系统', '架构'],
      targetPersonaId: 'tech-architect',
      weight: 0.85,
      enabled: true
    }
  ];

  constructor(
    aiService: AIService,
    personaService?: PersonaService
  ) {
    this.aiService = aiService;
    this.personaService = personaService || PersonaService.getInstance();
    
    // 默认配置
    this.defaultConfig = {
      personaMode: 'intelligent',
      enableMultiRound: false,
      maxPlanningDepth: 4,
      includeTimeEstimation: true
    };
  }

  /**
   * 执行任务规划
   * @param userGoal 用户目标描述
   * @param config 规划配置
   * @returns 规划结果
   */
  public async planTask(
    userGoal: string, 
    config: Partial<TaskPlanningConfig> = {}
  ): Promise<AgentResult<PlanningResult>> {
    const startTime = Date.now();
    const finalConfig = { ...this.defaultConfig, ...config };

    try {
      console.log('🚀 TaskPlannerAgent 开始任务规划:', {
        goal: userGoal.substring(0, 50) + '...',
        personaMode: finalConfig.personaMode
      });

      // 1. 选择合适的人设
      const personaResult = await this.selectPersona(userGoal, finalConfig);
      if (!personaResult.success || !personaResult.data) {
        return { success: false, error: `人设选择失败: ${personaResult.error}` };
      }

      const selectedPersona = personaResult.data;
      console.log('✅ 选择人设:', selectedPersona.name);

      // 2. 构建规划请求
      const planningRequest = this.buildPlanningRequest(userGoal, selectedPersona, finalConfig);

      // 3. 直接使用主AI服务进行规划
      const messages: AIMessage[] = [
        {
          id: 'system-' + Date.now(),
          type: 'system',
          content: this.buildSystemPrompt(selectedPersona),
          timestamp: Date.now()
        },
        {
          id: 'user-' + Date.now(),
          type: 'user',
          content: planningRequest.question,
          timestamp: Date.now()
        }
      ];

      const aiResult = await this.aiService.chatCompletion(messages, {
        temperature: 0.7,
        maxTokens: 10000
      });
      if (!aiResult.success || !aiResult.data) {
        return { success: false, error: `AI规划失败: ${aiResult.error}` };
      }

      console.log('✅ AI规划完成，开始解析结果');

      // 5. 解析AI响应为Markdown格式
      const messageContent = aiResult.data.choices[0]?.message?.content;
      const responseContent = typeof messageContent === 'string' ? messageContent :
        (Array.isArray(messageContent) ? messageContent.map(item => item.text || '').join('') : '');
      const markdownResult = await this.parseAIResponseToMarkdown(responseContent, userGoal);
      if (!markdownResult.success || !markdownResult.data) {
        return { success: false, error: `Markdown生成失败: ${markdownResult.error}` };
      }

      // 6. 构建规划结果
      const planningTime = Date.now() - startTime;
      const fileName = this.generateFileName(userGoal);
      const result: PlanningResult = {
        markdownContent: markdownResult.data,
        usedPersona: selectedPersona,
        statistics: {
          contentLength: markdownResult.data.length,
          planningTime,
          aiCallCount: 1,
          tokensUsed: aiResult.data.usage?.totalTokens || 0
        },
        metadata: {
          planningApproach: `基于${selectedPersona.name}的专业规划`,
          confidenceScore: 0.85,
          suggestedNextSteps: this.generateMarkdownNextSteps(),
          fileName
        }
      };

      console.log('🎉 TaskPlannerAgent 规划完成:', {
        contentLength: result.statistics.contentLength,
        planningTime: result.statistics.planningTime,
        persona: selectedPersona.name,
        fileName: result.metadata.fileName
      });

      return { success: true, data: result };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ TaskPlannerAgent 规划失败:', error);
      return { success: false, error: `任务规划异常: ${errorMessage}` };
    }
  }

  /**
   * 选择合适的人设
   */
  private async selectPersona(
    userGoal: string, 
    config: TaskPlanningConfig
  ): Promise<AgentResult<PersonaConfig>> {
    try {
      switch (config.personaMode) {
        case 'manual':
          // 手动模式：使用指定的人设ID
          if (!config.specificPersonaId) {
            return { success: false, error: '手动模式需要指定人设ID' };
          }
          const manualPersona = await this.personaService.getPersona(config.specificPersonaId);
          if (manualPersona.success && manualPersona.data) {
            return { success: true, data: manualPersona.data };
          }
          return { success: false, error: '指定的人设不存在' };

        case 'dedicated':
          // 专用模式：使用Agent专用人设
          const dedicatedPersona = await this.getOrCreateDedicatedPersona();
          return dedicatedPersona;

        case 'intelligent':
        default:
          // 智能模式：根据目标内容智能匹配
          const intelligentPersona = await this.intelligentPersonaSelection(userGoal);
          return intelligentPersona;
      }
    } catch (error) {
      return { success: false, error: `人设选择异常: ${error}` };
    }
  }

  /**
   * 智能人设选择
   */
  private async intelligentPersonaSelection(userGoal: string): Promise<AgentResult<PersonaConfig>> {
    try {
      // 分析用户目标，匹配最合适的人设
      const goalLower = userGoal.toLowerCase();
      let bestMatch: { rule: PersonaMatchingRule; score: number } | null = null;

      for (const rule of this.personaMatchingRules) {
        if (!rule.enabled) continue;

        let score = 0;
        for (const keyword of rule.keywords) {
          if (goalLower.includes(keyword)) {
            score += rule.weight;
          }
        }

        if (score > 0 && (!bestMatch || score > bestMatch.score)) {
          bestMatch = { rule, score };
        }
      }

      if (bestMatch) {
        // 尝试获取匹配的人设
        const personaResult = await this.personaService.getPersona(bestMatch.rule.targetPersonaId);
        if (personaResult.success && personaResult.data) {
          console.log('🎯 智能匹配人设:', bestMatch.rule.name, '得分:', bestMatch.score);
          return { success: true, data: personaResult.data };
        }
      }

      // 如果没有匹配到或获取失败，使用当前激活的人设
      console.log('🔄 使用当前激活人设作为备选');
      const activePersona = await this.personaService.getActivePersona();
      if (activePersona.success && activePersona.data) {
        return { success: true, data: activePersona.data };
      }

      // 最后的备选方案：创建默认规划专家人设
      return await this.createDefaultPlannerPersona();

    } catch (error) {
      return { success: false, error: `智能人设选择失败: ${error}` };
    }
  }

  /**
   * 获取或创建专用Agent人设
   */
  private async getOrCreateDedicatedPersona(): Promise<AgentResult<PersonaConfig>> {
    // 这里可以实现专用人设的获取或创建逻辑
    // 暂时使用默认规划专家
    return await this.createDefaultPlannerPersona();
  }

  /**
   * 创建默认规划专家人设
   */
  private async createDefaultPlannerPersona(): Promise<AgentResult<PersonaConfig>> {
    const defaultPersona: PersonaConfig = {
      id: 'default-task-planner',
      name: '任务规划专家',
      description: '我是一位专业的任务规划和项目管理专家，擅长将复杂的目标分解为清晰、可执行的任务步骤。我具备丰富的项目管理经验，能够合理估算时间，识别关键路径，并提供实用的执行建议。我会帮助你制定结构化、可操作的任务规划。',
      folderId: 'agent-dedicated',
      isActive: false,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    return { success: true, data: defaultPersona };
  }

  /**
   * 构建规划请求
   */
  private buildPlanningRequest(
    userGoal: string, 
    persona: PersonaConfig, 
    config: TaskPlanningConfig
  ): HelperRequest {
    const timeEstimationPrompt = config.includeTimeEstimation 
      ? '请为每个任务提供时间估算。' 
      : '';

    const depthPrompt = `请将任务分解为最多${config.maxPlanningDepth}层的层级结构。`;

    return {
      roleName: persona.name,
      roleDescription: persona.description,
      question: `请为以下目标制定详细的任务规划：

目标：${userGoal}

要求：
1. 将目标分解为具体、可执行的任务步骤
2. 按照逻辑顺序组织任务层级
3. ${depthPrompt}
4. ${timeEstimationPrompt}
5. 识别关键任务和依赖关系
6. 提供实用的执行建议

请以思维导图的格式输出，使用以下结构：
- 主目标作为根节点
- 主要阶段作为一级分支
- 具体任务作为二级分支
- 详细步骤作为三级分支

格式示例：
# 主目标
## 阶段一：准备工作
### 任务1：需求分析
#### 步骤1：收集信息
#### 步骤2：整理需求
### 任务2：资源准备
## 阶段二：执行阶段
### 任务3：具体实施
...`,
      timestamp: Date.now()
    };
  }

  /**
   * 构建系统提示词
   */
  private buildSystemPrompt(persona: PersonaConfig): string {
    return `你是${persona.name}。${persona.description}

请严格按照以下要求进行任务规划：

1. 分析用户目标，理解核心需求
2. 将目标分解为具体、可执行的任务步骤
3. 按照逻辑顺序组织任务层级
4. 使用Markdown格式输出，结构如下：
   - 使用 ## 表示主要阶段
   - 使用 ### 表示具体任务
   - 使用 #### 表示详细步骤
   - **重要：所有具体任务和详细步骤都必须使用任务状态标记**

5. 任务状态标记规范：
   - 使用 "[ ]" 表示未完成的任务（默认状态）
   - 使用 "[x]" 表示已完成的任务
   - 每个可执行的任务项都必须有状态标记
   - 示例：
     ## 第一阶段：准备工作
     [ ] 收集相关资料
     [ ] 制定详细计划
     [x] 确定项目目标（如果已完成）

6. 确保每个任务都是明确、可衡量的
7. 考虑任务之间的依赖关系
8. 提供实用的执行建议

请以专业、结构化的方式进行规划，确保输出的内容清晰易懂，并且所有任务都带有正确的状态标记。`;
  }

  /**
   * 解析AI响应为Markdown格式
   */
  private async parseAIResponseToMarkdown(
    aiResponse: string,
    originalGoal: string
  ): Promise<AgentResult<string>> {
    try {
      console.log('🔄 开始生成Markdown内容');

      // 构建完整的Markdown内容
      const timestamp = new Date().toLocaleString('zh-CN');
      const markdownContent = `# ${originalGoal}

${aiResponse}

---

*由Agent自动生成于 ${timestamp}*
*使用的AI模型: GPT-4*
*生成方式: 智能任务规划*`;

      console.log('✅ Markdown内容生成完成，长度:', markdownContent.length);
      return { success: true, data: markdownContent };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ Markdown生成失败:', error);
      return { success: false, error: `Markdown生成失败: ${errorMessage}` };
    }
  }

  /**
   * 解析AI响应为思维导图格式（已弃用，保留兼容性）
   */
  private async parseAIResponseToMindMap(
    aiResponse: string,
    originalGoal: string
  ): Promise<AgentResult<MindMapNode>> {
    try {
      console.log('🔄 开始解析AI响应为思维导图格式');

      // 解析Markdown格式的层级结构
      const lines = aiResponse.split('\n').filter(line => line.trim());
      const rootNode: MindMapNode = {
        data: {
          text: originalGoal,
          expand: true,
          isActive: false,
          uid: 'root-' + Date.now()
        },
        children: []
      };

      let currentLevel1Node: MindMapNode | null = null;
      let currentLevel2Node: MindMapNode | null = null;

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        // 解析不同级别的标题
        if (trimmedLine.startsWith('## ')) {
          // 一级分支
          const text = trimmedLine.replace('## ', '').trim();
          currentLevel1Node = {
            data: {
              text,
              expand: true,
              isActive: false,
              uid: 'level1-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11)
            },
            children: []
          };
          rootNode.children!.push(currentLevel1Node);
          currentLevel2Node = null;

        } else if (trimmedLine.startsWith('### ')) {
          // 二级分支
          const text = trimmedLine.replace('### ', '').trim();
          currentLevel2Node = {
            data: {
              text,
              expand: true,
              isActive: false,
              uid: 'level2-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11)
            },
            children: []
          };
          if (currentLevel1Node) {
            currentLevel1Node.children!.push(currentLevel2Node);
          }

        } else if (trimmedLine.startsWith('#### ')) {
          // 三级分支
          const text = trimmedLine.replace('#### ', '').trim();
          const level3Node: MindMapNode = {
            data: {
              text,
              expand: true,
              isActive: false,
              uid: 'level3-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11)
            },
            children: []
          };
          if (currentLevel2Node) {
            currentLevel2Node.children!.push(level3Node);
          } else if (currentLevel1Node) {
            currentLevel1Node.children!.push(level3Node);
          }
        }
      }

      // 如果解析结果为空，创建一个基本结构
      if (!rootNode.children || rootNode.children.length === 0) {
        console.log('⚠️ AI响应解析为空，创建基本结构');
        rootNode.children = [{
          data: {
            text: '规划阶段一：分析和准备',
            expand: true,
            isActive: false,
            uid: 'default-phase1-' + Date.now()
          },
          children: [{
            data: {
              text: '任务1：详细分析目标需求',
              expand: true,
              isActive: false,
              uid: 'default-task1-' + Date.now()
            },
            children: []
          }]
        }];
      }

      console.log('✅ 思维导图解析完成，节点数:', this.countNodes(rootNode));
      return { success: true, data: rootNode };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ 思维导图解析失败:', error);
      return { success: false, error: `解析失败: ${errorMessage}` };
    }
  }

  /**
   * 验证和优化思维导图结构
   */
  private validateMindMapStructure(mindMapData: MindMapNode): MindMapNode {
    // 确保所有节点都有必需的属性
    const validateNode = (node: MindMapNode): MindMapNode => {
      // 确保基本属性存在
      if (!node.data.uid) {
        node.data.uid = 'node-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11);
      }
      if (node.data.expand === undefined) {
        node.data.expand = true;
      }
      if (node.data.isActive === undefined) {
        node.data.isActive = false;
      }

      // 递归验证子节点
      if (node.children) {
        node.children = node.children.map(validateNode);
      }

      return node;
    };

    return validateNode(mindMapData);
  }

  /**
   * 计算节点数量
   */
  private countNodes(node: MindMapNode): number {
    let count = 1; // 当前节点
    if (node.children) {
      for (const child of node.children) {
        count += this.countNodes(child);
      }
    }
    return count;
  }

  /**
   * 生成文件名
   */
  private generateFileName(userGoal: string): string {
    // 清理用户目标，生成合适的文件名
    const cleanGoal = userGoal
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, '') // 只保留中文、英文、数字和空格
      .trim()
      .substring(0, 20); // 限制长度

    const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD格式
    return `${cleanGoal}_${timestamp}.md`;
  }

  /**
   * 生成Markdown格式的建议下一步操作
   */
  private generateMarkdownNextSteps(): string[] {
    return [
      '在AGENT文件夹中查看生成的规划文件',
      '根据规划内容调整和细化具体步骤',
      '为重要任务设置截止日期和优先级',
      '开始执行第一个阶段的任务',
      '定期回顾和更新规划进度'
    ];
  }

  /**
   * 生成建议的下一步操作（已弃用，保留兼容性）
   */
  private generateNextSteps(mindMapData: MindMapNode): string[] {
    const suggestions = [
      '审查和调整任务优先级',
      '为关键任务分配负责人',
      '设置任务截止日期和里程碑',
      '准备必要的资源和工具',
      '建立进度跟踪机制'
    ];

    // 根据思维导图的复杂度调整建议
    const nodeCount = this.countNodes(mindMapData);
    if (nodeCount > 20) {
      suggestions.unshift('考虑将大型任务进一步分解');
    }
    if (nodeCount < 5) {
      suggestions.push('考虑添加更多详细的执行步骤');
    }

    return suggestions.slice(0, 3); // 返回前3个建议
  }

  /**
   * 更新人设匹配规则
   */
  public updatePersonaMatchingRules(rules: PersonaMatchingRule[]): void {
    this.personaMatchingRules = rules;
    console.log('✅ 人设匹配规则已更新，共', rules.length, '条规则');
  }

  /**
   * 获取当前人设匹配规则
   */
  public getPersonaMatchingRules(): PersonaMatchingRule[] {
    return [...this.personaMatchingRules];
  }
}
